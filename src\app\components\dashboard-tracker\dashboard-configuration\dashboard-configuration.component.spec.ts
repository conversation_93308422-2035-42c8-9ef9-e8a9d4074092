import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DashboardConfigurationComponent } from './dashboard-configuration.component';
import { CUSTOM_ELEMENTS_SCHEMA, ChangeDetectorRef } from '@angular/core';
import { DashboardConfigurationConstants } from 'src/app/common/constants';
import { ITab } from 'projects/ng-neptune/src/lib/Tab/tab.model';
import { DashboardTrackerComponent } from '../dashboard-tracker.component';

// Mock DashboardConfigurationConstants
const mockConstants = {
  DashboardConfigurationTab: 'Dashboard Configuration',
  ManageTrackerFieldsTab: 'Manage Tracker Fields',
  DeletedColumnTab: 'Deleted Column',
  StatusFilterTab: 'Status Filter',
};

// Mock DashboardTrackerComponent
class MockDashboardTrackerComponent {
  validationErrors = new Map<string, string>();
  cellValueChanges: any[] = [];

  getPendingChangesCount(): number {
    return this.cellValueChanges.length;
  }

  saveCellValues(): void {}

  clearPendingChanges(): void {
    this.cellValueChanges = [];
    this.validationErrors.clear();
  }
}

// Mock ChangeDetectorRef
class MockChangeDetectorRef {
  detectChanges(): void {}
}

fdescribe('DashboardConfigurationComponent', () => {
  let component: DashboardConfigurationComponent;
  let fixture: ComponentFixture<DashboardConfigurationComponent>;
  let mockChangeDetectorRef: MockChangeDetectorRef;

  beforeEach(async () => {
    mockChangeDetectorRef = new MockChangeDetectorRef();

    await TestBed.configureTestingModule({
      declarations: [DashboardConfigurationComponent],
      providers: [
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardConfigurationComponent);
    component = fixture.componentInstance;

    // Mock the ViewChild dashboardTrackerComponent
    component.dashboardTrackerComponent = new MockDashboardTrackerComponent() as any;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set the first tab as selected by default', () => {
    expect(component.selectedTab).toBe(component.tabList[0]);
    expect(component.selectedTab.active).toBeTrue();
  });

  it('should activate the selected tab and deactivate others on tab click', () => {
    const tabToSelect: ITab = component.tabList[2];
    component.onTabClick(tabToSelect);
    expect(component.selectedTab).toBe(tabToSelect);
    component.tabList.forEach((tab, idx) => {
      if (idx === 2) {
        expect(tab.active).toBeTrue();
      } else {
        expect(tab.active).toBeFalse();
      }
    });
  });

  it('should have isDashboardConfigurationTab as true by default', () => {
    expect(component.isDashboardConfigurationTab).toBeTrue();
  });
  
  it('should activate ManageTrackerFieldsTab when navigateToDashboardConfig is called', () => {
    // Set all tabs inactive for clarity
    component.tabList.forEach(t => t.active = false);
    component.selectedTab = component.tabList[0];
    component.navigateToDashboardConfig();
    expect(component.tabList[1].active).toBeTrue();
    expect(component.selectedTab).toBe(component.tabList[1]);
    // Ensure other tabs are inactive
    expect(component.tabList[0].active).toBeFalse();
    expect(component.tabList[2].active).toBeFalse();
    expect(component.tabList[3].active).toBeFalse();
  });

  describe('onCellChangesUpdated', () => {
    it('should update pendingChangesCount and trigger change detection', () => {
      const changeCount = 5;
      spyOn(mockChangeDetectorRef, 'detectChanges');

      component.onCellChangesUpdated(changeCount);

      expect(component.pendingChangesCount).toBe(changeCount);
      expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    });

    it('should handle zero changes', () => {
      component.onCellChangesUpdated(0);
      expect(component.pendingChangesCount).toBe(0);
    });

    it('should handle negative changes (edge case)', () => {
      component.onCellChangesUpdated(-1);
      expect(component.pendingChangesCount).toBe(-1);
    });
  });

  describe('isSaveDisabled', () => {
    it('should return true when pendingChangesCount is 0', () => {
      component.pendingChangesCount = 0;
      expect(component.isSaveDisabled()).toBeTrue();
    });

    it('should return true when there are validation errors', () => {
      component.pendingChangesCount = 5;
      component.dashboardTrackerComponent.validationErrors.set('test_key', 'test error');

      expect(component.isSaveDisabled()).toBeTrue();
    });

    it('should return false when there are pending changes and no validation errors', () => {
      component.pendingChangesCount = 3;
      component.dashboardTrackerComponent.validationErrors.clear();

      expect(component.isSaveDisabled()).toBeFalse();
    });

    it('should return true when dashboardTrackerComponent is undefined', () => {
      component.pendingChangesCount = 5;
      component.dashboardTrackerComponent = undefined as any;

      expect(component.isSaveDisabled()).toBeTrue();
    });
  });

  describe('onSaveClick', () => {
    it('should call saveCellValues when there are pending changes', () => {
      const mockCellValue = {
        PortfolioCompanyId: 1,
        FundId: 1,
        ColumnId: 1,
        TimeSeriesID: 'test',
        CellValue: 'test data'
      };
      component.dashboardTrackerComponent.cellValueChanges = [mockCellValue];
      spyOn(component.dashboardTrackerComponent, 'getPendingChangesCount').and.returnValue(3);
      spyOn(component.dashboardTrackerComponent, 'saveCellValues');

      component.onSaveClick();

      expect(component.dashboardTrackerComponent.saveCellValues).toHaveBeenCalled();
    });

    it('should not call saveCellValues when there are no pending changes', () => {
      spyOn(component.dashboardTrackerComponent, 'getPendingChangesCount').and.returnValue(0);
      spyOn(component.dashboardTrackerComponent, 'saveCellValues');

      component.onSaveClick();

      expect(component.dashboardTrackerComponent.saveCellValues).not.toHaveBeenCalled();
    });

    it('should handle undefined dashboardTrackerComponent', () => {
      component.dashboardTrackerComponent = undefined as any;

      expect(() => component.onSaveClick()).not.toThrow();
    });
  });

  describe('onCancelClick', () => {
    it('should call clearPendingChanges when dashboardTrackerComponent exists', () => {
      spyOn(component.dashboardTrackerComponent, 'clearPendingChanges');

      component.onCancelClick();

      expect(component.dashboardTrackerComponent.clearPendingChanges).toHaveBeenCalled();
    });

    it('should handle undefined dashboardTrackerComponent', () => {
      component.dashboardTrackerComponent = undefined as any;

      expect(() => component.onCancelClick()).not.toThrow();
    });
  });

  describe('Component Properties', () => {
    it('should have correct tab constants', () => {
      expect(component.DashboardConfigurationTab).toBe('Dashboard Configuration');
      expect(component.ManageTrackerFieldsTab).toBe('Manage Tracker Fields');
      expect(component.DeletedColumnTab).toBe('Deleted Column');
      expect(component.StatusFilterTab).toBe('Status Filter');
    });

    it('should initialize with correct default values', () => {
      expect(component.isDashboardConfigurationTab).toBeTrue();
      expect(component.pendingChangesCount).toBe(0);
      expect(component.tabList.length).toBe(4);
      expect(component.selectedTab).toBe(component.tabList[0]);
    });

    it('should have correct tab structure', () => {
      expect(component.tabList[0].name).toBe('Dashboard Configuration');
      expect(component.tabList[0].active).toBeTrue();
      expect(component.tabList[1].name).toBe('Manage Tracker Fields');
      expect(component.tabList[1].active).toBeFalse();
      expect(component.tabList[2].name).toBe('Deleted Column');
      expect(component.tabList[2].active).toBeFalse();
      expect(component.tabList[3].name).toBe('Status Filter');
      expect(component.tabList[3].active).toBeFalse();
    });
  });
});
